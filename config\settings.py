"""
Application configuration settings for ArchiveApp.
"""
import os
from pathlib import Path

# Application information
APP_NAME = "ArchiveApp"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "Desktop Electronic Archiving System"

# Base directories
BASE_DIR = Path(__file__).parent.parent
STORAGE_DIR = BASE_DIR / "storage"
DATABASE_DIR = BASE_DIR / "data"

# Database settings
DATABASE_NAME = "archive.db"
DATABASE_PATH = DATABASE_DIR / DATABASE_NAME

# Storage directories
DOCUMENTS_DIR = STORAGE_DIR / "documents"
IMAGES_DIR = STORAGE_DIR / "images"
OTHERS_DIR = STORAGE_DIR / "others"

# File settings
SUPPORTED_DOCUMENT_TYPES = ['.pdf', '.docx', '.doc']
SUPPORTED_IMAGE_TYPES = ['.jpg', '.jpeg', '.png', '.gif', '.bmp']
SUPPORTED_FILE_TYPES = SUPPORTED_DOCUMENT_TYPES + SUPPORTED_IMAGE_TYPES

MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
MAX_TITLE_LENGTH = 255

# Categories
CATEGORIES = ["Documents", "Images", "Reports", "Other"]

# UI settings
THUMBNAIL_SIZE = (150, 150)
ITEMS_PER_PAGE = 50
SEARCH_DELAY_MS = 500  # Delay before executing search

# Logging settings
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE = BASE_DIR / "logs" / "archive.log"

def ensure_directories():
    """Create necessary directories if they don't exist."""
    directories = [
        STORAGE_DIR,
        DOCUMENTS_DIR,
        IMAGES_DIR,
        OTHERS_DIR,
        DATABASE_DIR,
        BASE_DIR / "logs"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
