"""
File upload dialog with metadata form for ArchiveApp.
"""
import os
import logging
from datetime import datetime
from typing import Optional
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit,
    QTextEdit, QComboBox, QPushButton, QFileDialog, QLabel,
    QMessageBox, QDateTimeEdit, QGroupBox, QProgressBar
)
from PyQt6.QtCore import Qt, QDateTime, QThread, pyqtSignal
from PyQt6.QtGui import QPixmap

from db.models import FileRecord, file_manager as db_file_manager
from models.file_manager import file_manager
from models.metadata import metadata_manager
from utils.helpers import thumbnail_generator, format_helper
from config.settings import CATEG<PERSON>IES, SUPPORTED_FILE_TYPES

logger = logging.getLogger(__name__)


class FileUploadWorker(QThread):
    """Worker thread for file upload operations."""
    
    progress_updated = pyqtSignal(int)
    upload_completed = pyqtSignal(bool, str, FileRecord)  # success, message, file_record
    
    def __init__(self, source_path: str, file_record: FileRecord):
        super().__init__()
        self.source_path = source_path
        self.file_record = file_record
    
    def run(self):
        """Run the upload process."""
        try:
            self.progress_updated.emit(25)
            
            # Copy file to archive
            success, dest_path, error_msg = file_manager.copy_file_to_archive(
                self.source_path, self.file_record.title
            )
            
            if not success:
                self.upload_completed.emit(False, error_msg, None)
                return
            
            self.progress_updated.emit(50)
            
            # Update file record with destination path
            self.file_record.file_path = dest_path
            
            # Get file info
            file_info = file_manager.get_file_info(dest_path)
            if file_info:
                self.file_record.file_size = file_info['size']
                self.file_record.file_type = file_info['extension']
            
            self.progress_updated.emit(75)
            
            # Save to database
            record_id = db_file_manager.create(self.file_record)
            self.file_record.id = record_id
            
            self.progress_updated.emit(100)
            
            self.upload_completed.emit(True, "File uploaded successfully", self.file_record)
            
        except Exception as e:
            logger.error(f"Upload error: {e}")
            self.upload_completed.emit(False, f"Upload failed: {str(e)}", None)


class UploadDialog(QDialog):
    """Dialog for uploading files and editing metadata."""
    
    def __init__(self, parent=None, edit_mode=False, file_record=None):
        super().__init__(parent)
        self.edit_mode = edit_mode
        self.file_record = file_record or FileRecord()
        self.selected_file_path = ""
        self.upload_worker = None
        
        self.setup_ui()
        self.setup_connections()
        
        if edit_mode and file_record:
            self.load_file_record(file_record)
        
        self.setModal(True)
    
    def setup_ui(self):
        """Setup the user interface."""
        title = "Edit File Metadata" if self.edit_mode else "Upload File"
        self.setWindowTitle(title)
        self.setMinimumSize(500, 600)
        
        layout = QVBoxLayout(self)
        
        # File selection group (only for upload mode)
        if not self.edit_mode:
            file_group = QGroupBox("File Selection")
            file_layout = QVBoxLayout(file_group)
            
            file_select_layout = QHBoxLayout()
            self.file_path_label = QLabel("No file selected")
            self.file_path_label.setStyleSheet("QLabel { border: 1px solid gray; padding: 5px; }")
            file_select_layout.addWidget(self.file_path_label)
            
            self.browse_button = QPushButton("Browse...")
            file_select_layout.addWidget(self.browse_button)
            
            file_layout.addLayout(file_select_layout)
            
            # File preview
            self.preview_label = QLabel()
            self.preview_label.setFixedSize(200, 150)
            self.preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.preview_label.setStyleSheet("QLabel { border: 1px solid gray; }")
            file_layout.addWidget(self.preview_label)
            
            layout.addWidget(file_group)
        
        # Metadata group
        metadata_group = QGroupBox("File Metadata")
        metadata_layout = QFormLayout(metadata_group)
        
        # Title (required)
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("Enter file title (required)")
        metadata_layout.addRow("Title*:", self.title_edit)
        
        # Description (optional)
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        self.description_edit.setPlaceholderText("Enter file description (optional)")
        metadata_layout.addRow("Description:", self.description_edit)
        
        # Category (required)
        self.category_combo = QComboBox()
        self.category_combo.addItems(CATEGORIES)
        metadata_layout.addRow("Category*:", self.category_combo)
        
        # Keywords (optional)
        self.keywords_edit = QLineEdit()
        self.keywords_edit.setPlaceholderText("Enter keywords separated by commas")
        metadata_layout.addRow("Keywords:", self.keywords_edit)
        
        # Upload date (auto-filled, editable)
        self.upload_date_edit = QDateTimeEdit()
        self.upload_date_edit.setDateTime(QDateTime.currentDateTime())
        self.upload_date_edit.setCalendarPopup(True)
        metadata_layout.addRow("Upload Date:", self.upload_date_edit)
        
        layout.addWidget(metadata_group)
        
        # Progress bar (hidden initially)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.cancel_button = QPushButton("Cancel")
        button_layout.addWidget(self.cancel_button)
        
        button_layout.addStretch()
        
        button_text = "Update" if self.edit_mode else "Upload"
        self.upload_button = QPushButton(button_text)
        self.upload_button.setDefault(True)
        button_layout.addWidget(self.upload_button)
        
        layout.addLayout(button_layout)
    
    def setup_connections(self):
        """Setup signal connections."""
        if not self.edit_mode:
            self.browse_button.clicked.connect(self.browse_file)
            self.title_edit.textChanged.connect(self.auto_suggest_keywords)
        
        self.cancel_button.clicked.connect(self.reject)
        self.upload_button.clicked.connect(self.process_file)
        
        # Enable/disable upload button based on required fields
        self.title_edit.textChanged.connect(self.validate_form)
        self.category_combo.currentTextChanged.connect(self.validate_form)
    
    def browse_file(self):
        """Open file browser to select file."""
        file_types = "Supported Files (" + " ".join(f"*{ext}" for ext in SUPPORTED_FILE_TYPES) + ")"
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select File to Upload", "", file_types
        )
        
        if file_path:
            self.selected_file_path = file_path
            self.file_path_label.setText(os.path.basename(file_path))
            
            # Auto-fill title if empty
            if not self.title_edit.text():
                base_name = os.path.splitext(os.path.basename(file_path))[0]
                self.title_edit.setText(base_name)
            
            # Auto-suggest category
            file_extension = os.path.splitext(file_path)[1].lower()
            suggested_category = metadata_manager.processor.suggest_category_from_extension(file_extension)
            category_index = self.category_combo.findText(suggested_category)
            if category_index >= 0:
                self.category_combo.setCurrentIndex(category_index)
            
            # Show preview
            self.show_file_preview(file_path)
            
            self.validate_form()
    
    def show_file_preview(self, file_path: str):
        """Show file preview/thumbnail.
        
        Args:
            file_path: Path to the file
        """
        pixmap = thumbnail_generator.generate_thumbnail(file_path)
        if pixmap:
            # Scale to fit preview area
            scaled_pixmap = pixmap.scaled(
                self.preview_label.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )
            self.preview_label.setPixmap(scaled_pixmap)
        else:
            self.preview_label.setText("Preview not available")
    
    def auto_suggest_keywords(self):
        """Auto-suggest keywords based on title."""
        if not self.edit_mode and not self.keywords_edit.text():
            title = self.title_edit.text()
            if title:
                suggested_keywords = metadata_manager.processor.extract_keywords_from_title(title)
                if suggested_keywords:
                    self.keywords_edit.setText(", ".join(suggested_keywords[:5]))  # Limit to 5 keywords
    
    def validate_form(self):
        """Validate form and enable/disable upload button."""
        title_valid = bool(self.title_edit.text().strip())
        category_valid = bool(self.category_combo.currentText())
        file_selected = self.edit_mode or bool(self.selected_file_path)
        
        self.upload_button.setEnabled(title_valid and category_valid and file_selected)
    
    def load_file_record(self, file_record: FileRecord):
        """Load file record data into form fields.
        
        Args:
            file_record: FileRecord to load
        """
        self.title_edit.setText(file_record.title)
        self.description_edit.setPlainText(file_record.description or "")
        
        category_index = self.category_combo.findText(file_record.category)
        if category_index >= 0:
            self.category_combo.setCurrentIndex(category_index)
        
        self.keywords_edit.setText(file_record.keywords or "")
        
        if file_record.upload_date:
            qt_datetime = QDateTime.fromString(
                file_record.upload_date.isoformat(), Qt.DateFormat.ISODate
            )
            self.upload_date_edit.setDateTime(qt_datetime)
    
    def get_file_record_from_form(self) -> FileRecord:
        """Create FileRecord from form data.
        
        Returns:
            FileRecord with form data
        """
        record = FileRecord() if not self.edit_mode else self.file_record
        
        # Get and normalize metadata
        title = self.title_edit.text().strip()
        description = self.description_edit.toPlainText().strip()
        keywords = self.keywords_edit.text().strip()
        
        normalized_title, normalized_description, normalized_keywords = \
            metadata_manager.normalize_all(title, description, keywords)
        
        record.title = normalized_title
        record.description = normalized_description
        record.category = self.category_combo.currentText()
        record.keywords = normalized_keywords
        record.upload_date = self.upload_date_edit.dateTime().toPython()
        
        if not self.edit_mode:
            record.original_filename = os.path.basename(self.selected_file_path)
        
        return record

    def process_file(self):
        """Process file upload or metadata update."""
        try:
            # Get form data
            record = self.get_file_record_from_form()

            # Validate metadata
            is_valid, errors = metadata_manager.validate_all(
                record.title, record.description, record.category,
                record.keywords, record.upload_date
            )

            if not is_valid:
                QMessageBox.warning(self, "Validation Error", "\n".join(errors))
                return

            if self.edit_mode:
                # Update existing record
                success = db_file_manager.update(record)
                if success:
                    QMessageBox.information(self, "Success", "Metadata updated successfully!")
                    self.accept()
                else:
                    QMessageBox.critical(self, "Error", "Failed to update metadata.")
            else:
                # Upload new file
                if not self.selected_file_path:
                    QMessageBox.warning(self, "No File Selected", "Please select a file to upload.")
                    return

                # Validate file
                is_valid, error_msg = file_manager.validate_file(self.selected_file_path)
                if not is_valid:
                    QMessageBox.warning(self, "File Validation Error", error_msg)
                    return

                # Start upload in worker thread
                self.start_upload(record)

        except Exception as e:
            logger.error(f"Error processing file: {e}")
            QMessageBox.critical(self, "Error", f"An error occurred: {str(e)}")

    def start_upload(self, record: FileRecord):
        """Start file upload in worker thread.

        Args:
            record: FileRecord to upload
        """
        # Disable UI during upload
        self.upload_button.setEnabled(False)
        self.browse_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # Create and start worker thread
        self.upload_worker = FileUploadWorker(self.selected_file_path, record)
        self.upload_worker.progress_updated.connect(self.progress_bar.setValue)
        self.upload_worker.upload_completed.connect(self.on_upload_completed)
        self.upload_worker.start()

    def on_upload_completed(self, success: bool, message: str, file_record: Optional[FileRecord]):
        """Handle upload completion.

        Args:
            success: Whether upload was successful
            message: Status message
            file_record: Uploaded FileRecord (if successful)
        """
        # Re-enable UI
        self.upload_button.setEnabled(True)
        self.browse_button.setEnabled(True)
        self.progress_bar.setVisible(False)

        if success:
            QMessageBox.information(self, "Success", message)
            self.accept()
        else:
            QMessageBox.critical(self, "Upload Failed", message)

        # Clean up worker
        if self.upload_worker:
            self.upload_worker.deleteLater()
            self.upload_worker = None

    def closeEvent(self, event):
        """Handle dialog close event."""
        # Cancel upload if in progress
        if self.upload_worker and self.upload_worker.isRunning():
            reply = QMessageBox.question(
                self, "Upload in Progress",
                "Upload is in progress. Do you want to cancel?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.upload_worker.terminate()
                self.upload_worker.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
