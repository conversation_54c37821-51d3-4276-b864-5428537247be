#!/usr/bin/env python3
"""
Test script to verify core functionality without GUI dependencies.
"""
import sys
import os
from pathlib import Path
from datetime import datetime

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

def test_configuration():
    """Test configuration loading."""
    print("Testing configuration...")
    try:
        from config.settings import APP_NAME, APP_VERSION, ensure_directories
        print(f"✓ App: {APP_NAME} v{APP_VERSION}")
        
        ensure_directories()
        print("✓ Directories created")
        
        return True
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        return False

def test_database():
    """Test database functionality."""
    print("\nTesting database...")
    try:
        from db.migrations import initialize_database
        from db.models import FileRecord, file_manager
        
        # Initialize database
        initialize_database()
        print("✓ Database initialized")
        
        # Test creating a record
        record = FileRecord(
            title="Test Document",
            description="Test description",
            file_path="/test/path/document.pdf",
            original_filename="document.pdf",
            file_size=1024,
            file_type=".pdf",
            category="Documents",
            keywords="test, document"
        )
        
        record_id = file_manager.create(record)
        print(f"✓ Record created with ID: {record_id}")
        
        # Test retrieving the record
        retrieved = file_manager.get_by_id(record_id)
        if retrieved and retrieved.title == "Test Document":
            print("✓ Record retrieved successfully")
        else:
            print("✗ Record retrieval failed")
            return False
        
        # Test searching
        results = file_manager.search(title="Test")
        if len(results) > 0:
            print(f"✓ Search found {len(results)} results")
        else:
            print("✗ Search failed")
            return False
        
        # Test updating
        retrieved.description = "Updated description"
        if file_manager.update(retrieved):
            print("✓ Record updated successfully")
        else:
            print("✗ Record update failed")
            return False
        
        # Test deleting
        if file_manager.delete(record_id):
            print("✓ Record deleted successfully")
        else:
            print("✗ Record deletion failed")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Database error: {e}")
        return False

def test_metadata():
    """Test metadata functionality."""
    print("\nTesting metadata...")
    try:
        from models.metadata import metadata_manager
        
        # Test validation
        is_valid, errors = metadata_manager.validate_all(
            title="Test Title",
            description="Test description",
            category="Documents",
            keywords="test, keywords",
            upload_date=datetime.now()
        )
        
        if is_valid:
            print("✓ Metadata validation passed")
        else:
            print(f"✗ Metadata validation failed: {errors}")
            return False
        
        # Test normalization
        title, desc, keywords = metadata_manager.normalize_all(
            title="  Test   Title  ",
            description="  Test description  ",
            keywords="keyword1, keyword2,  keyword1,  keyword3  "
        )
        
        if title == "Test Title" and keywords == "keyword1, keyword2, keyword3":
            print("✓ Metadata normalization passed")
        else:
            print("✗ Metadata normalization failed")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Metadata error: {e}")
        return False

def test_file_manager():
    """Test file manager functionality."""
    print("\nTesting file manager...")
    try:
        from models.file_manager import file_manager
        
        # Test file type detection
        if file_manager.is_supported_file_type("document.pdf"):
            print("✓ File type detection works")
        else:
            print("✗ File type detection failed")
            return False
        
        # Test storage directory selection
        from config.settings import DOCUMENTS_DIR
        storage_dir = file_manager.get_storage_directory('.pdf')
        if storage_dir == DOCUMENTS_DIR:
            print("✓ Storage directory selection works")
        else:
            print("✗ Storage directory selection failed")
            return False
        
        return True
    except Exception as e:
        print(f"✗ File manager error: {e}")
        return False

def test_utilities():
    """Test utility functions."""
    print("\nTesting utilities...")
    try:
        from utils.helpers import format_helper, file_type_detector
        
        # Test file size formatting
        size_str = format_helper.format_file_size(1024)
        if "1.0 KB" in size_str:
            print("✓ File size formatting works")
        else:
            print("✗ File size formatting failed")
            return False
        
        # Test file type detection
        file_type = file_type_detector.get_file_type("document.pdf")
        if file_type == ".pdf":
            print("✓ File type detection works")
        else:
            print("✗ File type detection failed")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Utilities error: {e}")
        return False

def main():
    """Run all tests."""
    print("ArchiveApp Core Functionality Test")
    print("=" * 40)
    
    tests = [
        test_configuration,
        test_database,
        test_metadata,
        test_file_manager,
        test_utilities
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print("Test failed, stopping...")
            break
    
    print("\n" + "=" * 40)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All core functionality tests passed!")
        print("\nThe application core is working correctly.")
        print("To run the full GUI application, install PyQt6:")
        print("  pip install PyQt6")
        print("Then run:")
        print("  python main.py")
        return 0
    else:
        print("✗ Some tests failed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
