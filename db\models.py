"""
Database models and ORM-like functions for ArchiveApp.
"""
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from dataclasses import dataclass

from .database import db_manager

logger = logging.getLogger(__name__)


@dataclass
class FileRecord:
    """Represents a file record in the database."""
    id: Optional[int] = None
    title: str = ""
    description: str = ""
    file_path: str = ""
    original_filename: str = ""
    file_size: int = 0
    file_type: str = ""
    category: str = ""
    keywords: str = ""
    upload_date: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'file_path': self.file_path,
            'original_filename': self.original_filename,
            'file_size': self.file_size,
            'file_type': self.file_type,
            'category': self.category,
            'keywords': self.keywords,
            'upload_date': self.upload_date.isoformat() if self.upload_date else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FileRecord':
        """Create FileRecord from dictionary."""
        record = cls()
        for key, value in data.items():
            if hasattr(record, key):
                if key in ['upload_date', 'created_at', 'updated_at'] and value:
                    setattr(record, key, datetime.fromisoformat(value))
                else:
                    setattr(record, key, value)
        return record


class FileRecordManager:
    """Manages file record operations in the database."""
    
    def create(self, record: FileRecord) -> int:
        """Create a new file record.
        
        Args:
            record: FileRecord to create
            
        Returns:
            ID of the created record
        """
        now = datetime.now()
        record.created_at = now
        record.updated_at = now
        
        if not record.upload_date:
            record.upload_date = now
        
        query = """
        INSERT INTO files (
            title, description, file_path, original_filename, file_size,
            file_type, category, keywords, upload_date, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        params = (
            record.title, record.description, record.file_path,
            record.original_filename, record.file_size, record.file_type,
            record.category, record.keywords, record.upload_date.isoformat(),
            record.created_at.isoformat(), record.updated_at.isoformat()
        )
        
        record_id = db_manager.execute_insert(query, params)
        record.id = record_id
        logger.info(f"Created file record with ID: {record_id}")
        return record_id
    
    def get_by_id(self, record_id: int) -> Optional[FileRecord]:
        """Get file record by ID.
        
        Args:
            record_id: ID of the record to retrieve
            
        Returns:
            FileRecord if found, None otherwise
        """
        query = "SELECT * FROM files WHERE id = ?"
        results = db_manager.execute_query(query, (record_id,))
        
        if results:
            return FileRecord.from_dict(results[0])
        return None
    
    def update(self, record: FileRecord) -> bool:
        """Update an existing file record.
        
        Args:
            record: FileRecord with updated data
            
        Returns:
            True if update was successful, False otherwise
        """
        if not record.id:
            logger.error("Cannot update record without ID")
            return False
        
        record.updated_at = datetime.now()
        
        query = """
        UPDATE files SET
            title = ?, description = ?, file_path = ?, original_filename = ?,
            file_size = ?, file_type = ?, category = ?, keywords = ?,
            upload_date = ?, updated_at = ?
        WHERE id = ?
        """
        
        params = (
            record.title, record.description, record.file_path,
            record.original_filename, record.file_size, record.file_type,
            record.category, record.keywords, record.upload_date.isoformat(),
            record.updated_at.isoformat(), record.id
        )
        
        rows_affected = db_manager.execute_update(query, params)
        success = rows_affected > 0
        
        if success:
            logger.info(f"Updated file record with ID: {record.id}")
        else:
            logger.warning(f"No record found with ID: {record.id}")
        
        return success
    
    def delete(self, record_id: int) -> bool:
        """Delete a file record.
        
        Args:
            record_id: ID of the record to delete
            
        Returns:
            True if deletion was successful, False otherwise
        """
        query = "DELETE FROM files WHERE id = ?"
        rows_affected = db_manager.execute_update(query, (record_id,))
        success = rows_affected > 0
        
        if success:
            logger.info(f"Deleted file record with ID: {record_id}")
        else:
            logger.warning(f"No record found with ID: {record_id}")
        
        return success

    def search(self, title: str = "", keywords: str = "", category: str = "",
               start_date: Optional[datetime] = None, end_date: Optional[datetime] = None,
               limit: int = 100, offset: int = 0) -> List[FileRecord]:
        """Search for file records based on criteria.

        Args:
            title: Title to search for (partial match)
            keywords: Keywords to search for
            category: Category to filter by
            start_date: Start date for date range filter
            end_date: End date for date range filter
            limit: Maximum number of results to return
            offset: Number of results to skip

        Returns:
            List of matching FileRecord objects
        """
        conditions = []
        params = []

        if title:
            conditions.append("title LIKE ?")
            params.append(f"%{title}%")

        if keywords:
            conditions.append("keywords LIKE ?")
            params.append(f"%{keywords}%")

        if category:
            conditions.append("category = ?")
            params.append(category)

        if start_date:
            conditions.append("upload_date >= ?")
            params.append(start_date.isoformat())

        if end_date:
            conditions.append("upload_date <= ?")
            params.append(end_date.isoformat())

        where_clause = " AND ".join(conditions) if conditions else "1=1"

        query = f"""
        SELECT * FROM files
        WHERE {where_clause}
        ORDER BY upload_date DESC
        LIMIT ? OFFSET ?
        """

        params.extend([limit, offset])
        results = db_manager.execute_query(query, tuple(params))

        return [FileRecord.from_dict(row) for row in results]

    def get_all(self, limit: int = 100, offset: int = 0) -> List[FileRecord]:
        """Get all file records with pagination.

        Args:
            limit: Maximum number of results to return
            offset: Number of results to skip

        Returns:
            List of FileRecord objects
        """
        query = """
        SELECT * FROM files
        ORDER BY upload_date DESC
        LIMIT ? OFFSET ?
        """

        results = db_manager.execute_query(query, (limit, offset))
        return [FileRecord.from_dict(row) for row in results]

    def count_all(self) -> int:
        """Get total count of file records.

        Returns:
            Total number of file records
        """
        query = "SELECT COUNT(*) as count FROM files"
        result = db_manager.execute_query(query)
        return result[0]['count'] if result else 0

    def count_search(self, title: str = "", keywords: str = "", category: str = "",
                    start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> int:
        """Count file records matching search criteria.

        Args:
            title: Title to search for (partial match)
            keywords: Keywords to search for
            category: Category to filter by
            start_date: Start date for date range filter
            end_date: End date for date range filter

        Returns:
            Number of matching records
        """
        conditions = []
        params = []

        if title:
            conditions.append("title LIKE ?")
            params.append(f"%{title}%")

        if keywords:
            conditions.append("keywords LIKE ?")
            params.append(f"%{keywords}%")

        if category:
            conditions.append("category = ?")
            params.append(category)

        if start_date:
            conditions.append("upload_date >= ?")
            params.append(start_date.isoformat())

        if end_date:
            conditions.append("upload_date <= ?")
            params.append(end_date.isoformat())

        where_clause = " AND ".join(conditions) if conditions else "1=1"

        query = f"SELECT COUNT(*) as count FROM files WHERE {where_clause}"
        result = db_manager.execute_query(query, tuple(params))
        return result[0]['count'] if result else 0


# Global file record manager instance
file_manager = FileRecordManager()
