"""
Unit tests for database operations.
"""
import pytest
import tempfile
import os
from datetime import datetime
from pathlib import Path
from db.database import DatabaseManager
from db.models import FileRecord, FileRecordManager
from db.migrations import MigrationManager


class TestDatabaseManager:
    """Test database manager operations."""
    
    @pytest.fixture
    def temp_db(self):
        """Create a temporary database for testing."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = Path(f.name)
        
        yield db_path
        
        # Cleanup
        if db_path.exists():
            db_path.unlink()
    
    @pytest.fixture
    def db_manager(self, temp_db):
        """Create a database manager with temporary database."""
        return DatabaseManager(temp_db)
    
    def test_database_connection(self, db_manager):
        """Test database connection."""
        with db_manager.get_connection() as conn:
            assert conn is not None
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            assert result[0] == 1
    
    def test_execute_query(self, db_manager):
        """Test query execution."""
        # Create a test table
        db_manager.execute_update("CREATE TABLE test (id INTEGER, name TEXT)")
        db_manager.execute_update("INSERT INTO test (id, name) VALUES (1, 'test')")
        
        # Query the data
        results = db_manager.execute_query("SELECT * FROM test")
        assert len(results) == 1
        assert results[0]['id'] == 1
        assert results[0]['name'] == 'test'
    
    def test_execute_insert(self, db_manager):
        """Test insert operation."""
        db_manager.execute_update("CREATE TABLE test (id INTEGER PRIMARY KEY, name TEXT)")
        row_id = db_manager.execute_insert("INSERT INTO test (name) VALUES (?)", ("test",))
        assert row_id > 0
    
    def test_table_exists(self, db_manager):
        """Test table existence check."""
        assert db_manager.table_exists("nonexistent") is False
        
        db_manager.execute_update("CREATE TABLE test (id INTEGER)")
        assert db_manager.table_exists("test") is True


class TestFileRecord:
    """Test FileRecord data class."""
    
    def test_file_record_creation(self):
        """Test FileRecord creation."""
        record = FileRecord(
            title="Test File",
            description="Test description",
            category="Documents",
            keywords="test, file"
        )
        assert record.title == "Test File"
        assert record.description == "Test description"
        assert record.category == "Documents"
        assert record.keywords == "test, file"
    
    def test_file_record_to_dict(self):
        """Test FileRecord to dictionary conversion."""
        now = datetime.now()
        record = FileRecord(
            id=1,
            title="Test File",
            upload_date=now
        )
        
        data = record.to_dict()
        assert data['id'] == 1
        assert data['title'] == "Test File"
        assert data['upload_date'] == now.isoformat()
    
    def test_file_record_from_dict(self):
        """Test FileRecord creation from dictionary."""
        now = datetime.now()
        data = {
            'id': 1,
            'title': 'Test File',
            'upload_date': now.isoformat()
        }
        
        record = FileRecord.from_dict(data)
        assert record.id == 1
        assert record.title == 'Test File'
        assert record.upload_date == now


class TestFileRecordManager:
    """Test FileRecordManager operations."""
    
    @pytest.fixture
    def temp_db(self):
        """Create a temporary database for testing."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = Path(f.name)
        
        # Initialize database with migrations
        from db.database import db_manager
        original_path = db_manager.db_path
        db_manager.db_path = db_path
        
        migration_manager = MigrationManager()
        migration_manager.apply_migrations()
        
        yield db_path
        
        # Restore original path and cleanup
        db_manager.db_path = original_path
        if db_path.exists():
            db_path.unlink()
    
    @pytest.fixture
    def record_manager(self, temp_db):
        """Create a FileRecordManager with temporary database."""
        from db.database import db_manager
        original_path = db_manager.db_path
        db_manager.db_path = temp_db
        
        manager = FileRecordManager()
        
        yield manager
        
        # Restore original path
        db_manager.db_path = original_path
    
    def test_create_record(self, record_manager):
        """Test creating a file record."""
        record = FileRecord(
            title="Test File",
            description="Test description",
            file_path="/path/to/file.pdf",
            original_filename="file.pdf",
            file_size=1024,
            file_type=".pdf",
            category="Documents",
            keywords="test, file"
        )
        
        record_id = record_manager.create(record)
        assert record_id > 0
        assert record.id == record_id
    
    def test_get_by_id(self, record_manager):
        """Test retrieving a record by ID."""
        # Create a record first
        record = FileRecord(
            title="Test File",
            category="Documents"
        )
        record_id = record_manager.create(record)
        
        # Retrieve it
        retrieved = record_manager.get_by_id(record_id)
        assert retrieved is not None
        assert retrieved.id == record_id
        assert retrieved.title == "Test File"
    
    def test_update_record(self, record_manager):
        """Test updating a record."""
        # Create a record first
        record = FileRecord(
            title="Original Title",
            category="Documents"
        )
        record_id = record_manager.create(record)
        
        # Update it
        record.title = "Updated Title"
        success = record_manager.update(record)
        assert success is True
        
        # Verify update
        retrieved = record_manager.get_by_id(record_id)
        assert retrieved.title == "Updated Title"
    
    def test_delete_record(self, record_manager):
        """Test deleting a record."""
        # Create a record first
        record = FileRecord(
            title="Test File",
            category="Documents"
        )
        record_id = record_manager.create(record)
        
        # Delete it
        success = record_manager.delete(record_id)
        assert success is True
        
        # Verify deletion
        retrieved = record_manager.get_by_id(record_id)
        assert retrieved is None
    
    def test_search_records(self, record_manager):
        """Test searching records."""
        # Create test records
        record1 = FileRecord(title="Python Tutorial", category="Documents", keywords="python, programming")
        record2 = FileRecord(title="Java Guide", category="Documents", keywords="java, programming")
        record3 = FileRecord(title="Photo Album", category="Images", keywords="photos, vacation")
        
        record_manager.create(record1)
        record_manager.create(record2)
        record_manager.create(record3)
        
        # Search by title
        results = record_manager.search(title="Python")
        assert len(results) == 1
        assert results[0].title == "Python Tutorial"
        
        # Search by keywords
        results = record_manager.search(keywords="programming")
        assert len(results) == 2
        
        # Search by category
        results = record_manager.search(category="Images")
        assert len(results) == 1
        assert results[0].title == "Photo Album"


if __name__ == "__main__":
    pytest.main([__file__])
