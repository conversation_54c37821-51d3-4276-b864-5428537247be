"""
Utility functions and helpers for ArchiveApp.
"""
import os
import mimetypes
import logging
from pathlib import Path
from typing import Optional, <PERSON><PERSON>
try:
    from PIL import Image, ImageQt
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    from PyQt6.QtGui import QPixmap, QIcon
    from PyQt6.QtCore import QSize
    PYQT6_AVAILABLE = True
except ImportError:
    PYQT6_AVAILABLE = False
    # Create dummy classes for when PyQt6 is not available
    class QPixmap:
        def __init__(self, *args, **kwargs):
            pass
        def scaled(self, *args, **kwargs):
            return self
        def fill(self, *args, **kwargs):
            pass
        @staticmethod
        def fromImage(*args, **kwargs):
            return QPixmap()

    class QIcon:
        def __init__(self, *args, **kwargs):
            pass

    class QSize:
        def __init__(self, width=16, height=16):
            self.width = width
            self.height = height
        def width(self):
            return self.width
        def height(self):
            return self.height

from config.settings import THUMBNAIL_SIZE, SUPPORTED_IMAGE_TYPES

logger = logging.getLogger(__name__)


class FileTypeDetector:
    """Detects and validates file types."""
    
    @staticmethod
    def get_file_type(file_path: str) -> str:
        """Get file type based on extension and MIME type.
        
        Args:
            file_path: Path to the file
            
        Returns:
            File type string (extension)
        """
        file_path = Path(file_path)
        extension = file_path.suffix.lower()
        
        # Use extension as primary method
        if extension:
            return extension
        
        # Fallback to MIME type detection
        try:
            mime_type, _ = mimetypes.guess_type(str(file_path))
            if mime_type:
                # Map common MIME types to extensions
                mime_to_ext = {
                    'application/pdf': '.pdf',
                    'application/msword': '.doc',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
                    'image/jpeg': '.jpg',
                    'image/png': '.png',
                    'image/gif': '.gif',
                    'image/bmp': '.bmp'
                }
                return mime_to_ext.get(mime_type, '.unknown')
        except Exception as e:
            logger.debug(f"Could not detect MIME type for {file_path}: {e}")
        
        return '.unknown'
    
    @staticmethod
    def is_image_file(file_path: str) -> bool:
        """Check if file is an image.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if file is an image, False otherwise
        """
        extension = FileTypeDetector.get_file_type(file_path)
        return extension in SUPPORTED_IMAGE_TYPES
    
    @staticmethod
    def is_document_file(file_path: str) -> bool:
        """Check if file is a document.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if file is a document, False otherwise
        """
        from config.settings import SUPPORTED_DOCUMENT_TYPES
        extension = FileTypeDetector.get_file_type(file_path)
        return extension in SUPPORTED_DOCUMENT_TYPES


class ThumbnailGenerator:
    """Generates thumbnails for files."""
    
    def __init__(self):
        self.thumbnail_size = THUMBNAIL_SIZE
    
    def generate_image_thumbnail(self, image_path: str) -> Optional[QPixmap]:
        """Generate thumbnail for an image file.

        Args:
            image_path: Path to the image file

        Returns:
            QPixmap thumbnail or None if generation failed
        """
        if not PIL_AVAILABLE or not PYQT6_AVAILABLE:
            logger.warning("PIL or PyQt6 not available, cannot generate image thumbnails")
            return None

        try:
            # Open image with PIL
            with Image.open(image_path) as img:
                # Convert to RGB if necessary (for PNG with transparency, etc.)
                if img.mode in ('RGBA', 'LA', 'P'):
                    # Create white background
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Create thumbnail
                img.thumbnail(self.thumbnail_size, Image.Resampling.LANCZOS)
                
                # Convert to QPixmap
                qt_image = ImageQt.ImageQt(img)
                pixmap = QPixmap.fromImage(qt_image)
                
                return pixmap
                
        except Exception as e:
            logger.error(f"Failed to generate thumbnail for {image_path}: {e}")
            return None
    
    def generate_document_thumbnail(self, document_path: str) -> Optional[QPixmap]:
        """Generate thumbnail for a document file.
        
        Args:
            document_path: Path to the document file
            
        Returns:
            QPixmap thumbnail or None if generation failed
        """
        # For now, return a generic document icon
        # In a more advanced implementation, you could use libraries like
        # pdf2image for PDFs or python-docx for Word documents
        try:
            # Create a simple document icon
            pixmap = QPixmap(self.thumbnail_size[0], self.thumbnail_size[1])
            pixmap.fill()  # Fill with white
            
            # You could draw a document icon here using QPainter
            # For now, just return the white pixmap
            return pixmap
            
        except Exception as e:
            logger.error(f"Failed to generate document thumbnail for {document_path}: {e}")
            return None
    
    def generate_thumbnail(self, file_path: str) -> Optional[QPixmap]:
        """Generate thumbnail for any supported file type.
        
        Args:
            file_path: Path to the file
            
        Returns:
            QPixmap thumbnail or None if generation failed
        """
        if not os.path.exists(file_path):
            logger.warning(f"File does not exist: {file_path}")
            return None
        
        if FileTypeDetector.is_image_file(file_path):
            return self.generate_image_thumbnail(file_path)
        elif FileTypeDetector.is_document_file(file_path):
            return self.generate_document_thumbnail(file_path)
        else:
            # Generic file icon
            return self.generate_generic_thumbnail()
    
    def generate_generic_thumbnail(self) -> QPixmap:
        """Generate a generic file thumbnail.
        
        Returns:
            QPixmap with generic file icon
        """
        pixmap = QPixmap(self.thumbnail_size[0], self.thumbnail_size[1])
        pixmap.fill()  # Fill with white
        return pixmap


class IconProvider:
    """Provides icons for different file types."""
    
    def __init__(self):
        self._icon_cache = {}
    
    def get_file_icon(self, file_path: str, size: QSize = QSize(16, 16)) -> QIcon:
        """Get icon for a file based on its type.
        
        Args:
            file_path: Path to the file
            size: Size of the icon
            
        Returns:
            QIcon for the file type
        """
        extension = FileTypeDetector.get_file_type(file_path)
        cache_key = f"{extension}_{size.width()}x{size.height()}"
        
        if cache_key in self._icon_cache:
            return self._icon_cache[cache_key]
        
        # Create icon based on file type
        icon = self._create_file_type_icon(extension, size)
        self._icon_cache[cache_key] = icon
        
        return icon
    
    def _create_file_type_icon(self, extension: str, size: QSize) -> QIcon:
        """Create an icon for a specific file type.
        
        Args:
            extension: File extension
            size: Icon size
            
        Returns:
            QIcon for the file type
        """
        # For now, create a simple colored square based on file type
        # In a real application, you would use proper icon files
        
        pixmap = QPixmap(size)
        
        # Color based on file type
        if extension in ['.pdf']:
            pixmap.fill('#FF0000')  # Red for PDF
        elif extension in ['.doc', '.docx']:
            pixmap.fill('#0000FF')  # Blue for Word documents
        elif extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
            pixmap.fill('#00FF00')  # Green for images
        else:
            pixmap.fill('#808080')  # Gray for others
        
        return QIcon(pixmap)


class FormatHelper:
    """Helper functions for formatting data."""
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """Format file size in human-readable format.
        
        Args:
            size_bytes: Size in bytes
            
        Returns:
            Formatted size string
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
    
    @staticmethod
    def format_date(date_obj) -> str:
        """Format datetime object for display.
        
        Args:
            date_obj: datetime object
            
        Returns:
            Formatted date string
        """
        if not date_obj:
            return ""
        
        return date_obj.strftime("%Y-%m-%d %H:%M")
    
    @staticmethod
    def truncate_text(text: str, max_length: int = 50) -> str:
        """Truncate text to specified length.
        
        Args:
            text: Text to truncate
            max_length: Maximum length
            
        Returns:
            Truncated text with ellipsis if needed
        """
        if not text:
            return ""
        
        if len(text) <= max_length:
            return text
        
        return text[:max_length - 3] + "..."


# Global instances
file_type_detector = FileTypeDetector()
thumbnail_generator = ThumbnailGenerator()
icon_provider = IconProvider()
format_helper = FormatHelper()
