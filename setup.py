#!/usr/bin/env python3
"""
Setup script for ArchiveApp installation.
"""
import os
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required.")
        print(f"Current version: {sys.version}")
        return False
    return True


def install_dependencies():
    """Install required dependencies."""
    print("Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("Dependencies installed successfully.")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error installing dependencies: {e}")
        return False


def create_directories():
    """Create necessary application directories."""
    print("Creating application directories...")
    try:
        from config.settings import ensure_directories
        ensure_directories()
        print("Directories created successfully.")
        return True
    except Exception as e:
        print(f"Error creating directories: {e}")
        return False


def initialize_database():
    """Initialize the application database."""
    print("Initializing database...")
    try:
        from db.migrations import initialize_database
        initialize_database()
        print("Database initialized successfully.")
        return True
    except Exception as e:
        print(f"Error initializing database: {e}")
        return False


def run_tests():
    """Run unit tests to verify installation."""
    print("Running tests...")
    try:
        subprocess.check_call([sys.executable, "-m", "pytest", "tests/", "-v"])
        print("All tests passed.")
        return True
    except subprocess.CalledProcessError:
        print("Some tests failed. Installation may have issues.")
        return False
    except FileNotFoundError:
        print("pytest not found. Skipping tests.")
        return True


def main():
    """Main setup function."""
    print("ArchiveApp Setup")
    print("================")
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Install dependencies
    if not install_dependencies():
        return 1
    
    # Create directories
    if not create_directories():
        return 1
    
    # Initialize database
    if not initialize_database():
        return 1
    
    # Run tests (optional)
    run_tests()
    
    print("\nSetup completed successfully!")
    print("You can now run the application with: python main.py")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
