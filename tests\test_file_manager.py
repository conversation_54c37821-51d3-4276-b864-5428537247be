"""
Unit tests for file management operations.
"""
import pytest
import tempfile
import os
from pathlib import Path
from models.file_manager import FileManager


class TestFileManager:
    """Test file manager operations."""
    
    @pytest.fixture
    def temp_file(self):
        """Create a temporary test file."""
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            f.write(b"Test file content")
            temp_path = f.name
        
        yield temp_path
        
        # Cleanup
        if os.path.exists(temp_path):
            os.unlink(temp_path)
    
    @pytest.fixture
    def file_manager(self):
        """Create a file manager instance."""
        return FileManager()
    
    def test_is_supported_file_type_pdf(self, file_manager):
        """Test PDF file type detection."""
        assert file_manager.is_supported_file_type("document.pdf") is True
    
    def test_is_supported_file_type_image(self, file_manager):
        """Test image file type detection."""
        assert file_manager.is_supported_file_type("image.jpg") is True
        assert file_manager.is_supported_file_type("image.png") is True
    
    def test_is_supported_file_type_unsupported(self, file_manager):
        """Test unsupported file type detection."""
        assert file_manager.is_supported_file_type("file.xyz") is False
    
    def test_get_storage_directory_documents(self, file_manager):
        """Test storage directory selection for documents."""
        from config.settings import DOCUMENTS_DIR
        storage_dir = file_manager.get_storage_directory('.pdf')
        assert storage_dir == DOCUMENTS_DIR
    
    def test_get_storage_directory_images(self, file_manager):
        """Test storage directory selection for images."""
        from config.settings import IMAGES_DIR
        storage_dir = file_manager.get_storage_directory('.jpg')
        assert storage_dir == IMAGES_DIR
    
    def test_get_storage_directory_others(self, file_manager):
        """Test storage directory selection for other files."""
        from config.settings import OTHERS_DIR
        storage_dir = file_manager.get_storage_directory('.xyz')
        assert storage_dir == OTHERS_DIR
    
    def test_validate_file_exists(self, file_manager, temp_file):
        """Test file validation for existing file."""
        # Create a supported file type
        pdf_file = temp_file.replace('.txt', '.pdf')
        os.rename(temp_file, pdf_file)
        
        is_valid, error = file_manager.validate_file(pdf_file)
        # Note: This might fail due to file type validation, but file existence should pass
        assert "does not exist" not in error
        
        # Cleanup
        if os.path.exists(pdf_file):
            os.unlink(pdf_file)
    
    def test_validate_file_not_exists(self, file_manager):
        """Test file validation for non-existing file."""
        is_valid, error = file_manager.validate_file("nonexistent.pdf")
        assert is_valid is False
        assert "does not exist" in error
    
    def test_generate_unique_filename(self, file_manager):
        """Test unique filename generation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Test with non-existing file
            filename = file_manager.generate_unique_filename("test.txt", temp_path)
            assert filename == "test.txt"
            
            # Create the file and test again
            (temp_path / "test.txt").touch()
            filename = file_manager.generate_unique_filename("test.txt", temp_path)
            assert filename == "test_1.txt"
            
            # Create that file too
            (temp_path / "test_1.txt").touch()
            filename = file_manager.generate_unique_filename("test.txt", temp_path)
            assert filename == "test_2.txt"
    
    def test_get_file_info_exists(self, file_manager, temp_file):
        """Test getting file info for existing file."""
        info = file_manager.get_file_info(temp_file)
        assert info is not None
        assert info['name'] == os.path.basename(temp_file)
        assert info['size'] > 0
        assert info['exists'] is True
    
    def test_get_file_info_not_exists(self, file_manager):
        """Test getting file info for non-existing file."""
        info = file_manager.get_file_info("nonexistent.txt")
        assert info is None


if __name__ == "__main__":
    pytest.main([__file__])
