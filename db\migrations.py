"""
Database schema setup and migrations for ArchiveApp.
"""
import logging
from typing import List

from .database import db_manager

logger = logging.getLogger(__name__)


class Migration:
    """Base class for database migrations."""
    
    def __init__(self, version: int, description: str):
        self.version = version
        self.description = description
    
    def up(self):
        """Apply the migration."""
        raise NotImplementedError
    
    def down(self):
        """Rollback the migration."""
        raise NotImplementedError


class InitialMigration(Migration):
    """Initial database schema migration."""
    
    def __init__(self):
        super().__init__(1, "Create initial files table")
    
    def up(self):
        """Create the files table and indexes."""
        # Create files table
        create_files_table = """
        CREATE TABLE IF NOT EXISTS files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            file_path VARCHAR(500) NOT NULL,
            original_filename VARCHAR(255) NOT NULL,
            file_size INTEGER NOT NULL DEFAULT 0,
            file_type VARCHAR(50) NOT NULL,
            category VARCHAR(100) NOT NULL,
            keywords TEXT,
            upload_date DATETIME NOT NULL,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NOT NULL
        )
        """
        
        # Create indexes for better search performance
        create_title_index = """
        CREATE INDEX IF NOT EXISTS idx_files_title ON files(title)
        """
        
        create_category_index = """
        CREATE INDEX IF NOT EXISTS idx_files_category ON files(category)
        """
        
        create_upload_date_index = """
        CREATE INDEX IF NOT EXISTS idx_files_upload_date ON files(upload_date)
        """
        
        create_keywords_index = """
        CREATE INDEX IF NOT EXISTS idx_files_keywords ON files(keywords)
        """
        
        # Create migration tracking table
        create_migrations_table = """
        CREATE TABLE IF NOT EXISTS migrations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            version INTEGER NOT NULL UNIQUE,
            description TEXT NOT NULL,
            applied_at DATETIME NOT NULL
        )
        """
        
        try:
            db_manager.execute_update(create_files_table)
            db_manager.execute_update(create_title_index)
            db_manager.execute_update(create_category_index)
            db_manager.execute_update(create_upload_date_index)
            db_manager.execute_update(create_keywords_index)
            db_manager.execute_update(create_migrations_table)
            logger.info("Initial migration applied successfully")
        except Exception as e:
            logger.error(f"Failed to apply initial migration: {e}")
            raise
    
    def down(self):
        """Drop all tables."""
        try:
            db_manager.execute_update("DROP TABLE IF EXISTS files")
            db_manager.execute_update("DROP TABLE IF EXISTS migrations")
            logger.info("Initial migration rolled back successfully")
        except Exception as e:
            logger.error(f"Failed to rollback initial migration: {e}")
            raise


class MigrationManager:
    """Manages database migrations."""
    
    def __init__(self):
        self.migrations: List[Migration] = [
            InitialMigration(),
        ]
    
    def get_applied_migrations(self) -> List[int]:
        """Get list of applied migration versions."""
        try:
            query = "SELECT version FROM migrations ORDER BY version"
            results = db_manager.execute_query(query)
            return [row['version'] for row in results]
        except Exception:
            # Migrations table doesn't exist yet
            return []
    
    def record_migration(self, migration: Migration):
        """Record a migration as applied."""
        from datetime import datetime
        
        query = """
        INSERT INTO migrations (version, description, applied_at)
        VALUES (?, ?, ?)
        """
        
        params = (migration.version, migration.description, datetime.now().isoformat())
        db_manager.execute_insert(query, params)
    
    def apply_migrations(self):
        """Apply all pending migrations."""
        applied_versions = self.get_applied_migrations()
        
        for migration in self.migrations:
            if migration.version not in applied_versions:
                logger.info(f"Applying migration {migration.version}: {migration.description}")
                migration.up()
                self.record_migration(migration)
                logger.info(f"Migration {migration.version} applied successfully")
            else:
                logger.debug(f"Migration {migration.version} already applied")
    
    def rollback_migration(self, version: int):
        """Rollback a specific migration."""
        migration = next((m for m in self.migrations if m.version == version), None)
        
        if not migration:
            raise ValueError(f"Migration version {version} not found")
        
        applied_versions = self.get_applied_migrations()
        
        if version not in applied_versions:
            raise ValueError(f"Migration version {version} is not applied")
        
        logger.info(f"Rolling back migration {version}: {migration.description}")
        migration.down()
        
        # Remove from migrations table
        query = "DELETE FROM migrations WHERE version = ?"
        db_manager.execute_update(query, (version,))
        
        logger.info(f"Migration {version} rolled back successfully")


def initialize_database():
    """Initialize the database with all migrations."""
    logger.info("Initializing database...")
    migration_manager = MigrationManager()
    migration_manager.apply_migrations()
    logger.info("Database initialization complete")


# Global migration manager instance
migration_manager = MigrationManager()
