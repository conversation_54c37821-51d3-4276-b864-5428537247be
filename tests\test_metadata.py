"""
Unit tests for metadata handling and validation.
"""
import pytest
from datetime import datetime
from models.metadata import MetadataValidator, MetadataProcessor, MetadataManager


class TestMetadataValidator:
    """Test metadata validation functions."""
    
    def test_validate_title_valid(self):
        """Test valid title validation."""
        is_valid, error = MetadataValidator.validate_title("Valid Title")
        assert is_valid is True
        assert error == ""
    
    def test_validate_title_empty(self):
        """Test empty title validation."""
        is_valid, error = MetadataValidator.validate_title("")
        assert is_valid is False
        assert "required" in error.lower()
    
    def test_validate_title_too_long(self):
        """Test title that's too long."""
        long_title = "x" * 300  # Exceeds MAX_TITLE_LENGTH
        is_valid, error = MetadataValidator.validate_title(long_title)
        assert is_valid is False
        assert "characters" in error.lower()
    
    def test_validate_title_invalid_chars(self):
        """Test title with invalid characters."""
        invalid_title = "Title with <invalid> chars"
        is_valid, error = MetadataValidator.validate_title(invalid_title)
        assert is_valid is False
        assert "invalid character" in error.lower()
    
    def test_validate_description_valid(self):
        """Test valid description validation."""
        is_valid, error = MetadataValidator.validate_description("Valid description")
        assert is_valid is True
        assert error == ""
    
    def test_validate_description_empty(self):
        """Test empty description (should be valid)."""
        is_valid, error = MetadataValidator.validate_description("")
        assert is_valid is True
        assert error == ""
    
    def test_validate_category_valid(self):
        """Test valid category validation."""
        is_valid, error = MetadataValidator.validate_category("Documents")
        assert is_valid is True
        assert error == ""
    
    def test_validate_category_invalid(self):
        """Test invalid category validation."""
        is_valid, error = MetadataValidator.validate_category("InvalidCategory")
        assert is_valid is False
        assert "must be one of" in error.lower()
    
    def test_validate_keywords_valid(self):
        """Test valid keywords validation."""
        is_valid, error = MetadataValidator.validate_keywords("keyword1, keyword2")
        assert is_valid is True
        assert error == ""
    
    def test_validate_upload_date_valid(self):
        """Test valid upload date validation."""
        now = datetime.now()
        is_valid, error = MetadataValidator.validate_upload_date(now)
        assert is_valid is True
        assert error == ""
    
    def test_validate_upload_date_future(self):
        """Test future upload date (should be invalid)."""
        future_date = datetime(2030, 1, 1)
        is_valid, error = MetadataValidator.validate_upload_date(future_date)
        assert is_valid is False
        assert "future" in error.lower()


class TestMetadataProcessor:
    """Test metadata processing functions."""
    
    def test_normalize_title(self):
        """Test title normalization."""
        title = "  Title   with   extra   spaces  "
        normalized = MetadataProcessor.normalize_title(title)
        assert normalized == "Title with extra spaces"
    
    def test_normalize_keywords(self):
        """Test keywords normalization."""
        keywords = "keyword1, keyword2,  keyword1,  keyword3  "
        normalized = MetadataProcessor.normalize_keywords(keywords)
        assert normalized == "keyword1, keyword2, keyword3"
    
    def test_extract_keywords_from_title(self):
        """Test keyword extraction from title."""
        title = "Python Programming Tutorial for Beginners"
        keywords = MetadataProcessor.extract_keywords_from_title(title)
        assert "python" in keywords
        assert "programming" in keywords
        assert "tutorial" in keywords
        assert "beginners" in keywords
        # Common words should be filtered out
        assert "for" not in keywords
    
    def test_suggest_category_from_extension(self):
        """Test category suggestion from file extension."""
        assert MetadataProcessor.suggest_category_from_extension(".pdf") == "Documents"
        assert MetadataProcessor.suggest_category_from_extension(".jpg") == "Images"
        assert MetadataProcessor.suggest_category_from_extension(".unknown") == "Other"


class TestMetadataManager:
    """Test metadata manager integration."""
    
    def test_validate_all_valid(self):
        """Test validation of all valid metadata."""
        manager = MetadataManager()
        is_valid, errors = manager.validate_all(
            title="Valid Title",
            description="Valid description",
            category="Documents",
            keywords="keyword1, keyword2",
            upload_date=datetime.now()
        )
        assert is_valid is True
        assert len(errors) == 0
    
    def test_validate_all_invalid(self):
        """Test validation with invalid metadata."""
        manager = MetadataManager()
        is_valid, errors = manager.validate_all(
            title="",  # Invalid: empty
            description="Valid description",
            category="InvalidCategory",  # Invalid: not in CATEGORIES
            keywords="valid keywords",
            upload_date=datetime(2030, 1, 1)  # Invalid: future date
        )
        assert is_valid is False
        assert len(errors) > 0
    
    def test_normalize_all(self):
        """Test normalization of all metadata fields."""
        manager = MetadataManager()
        title, description, keywords = manager.normalize_all(
            title="  Title  with  spaces  ",
            description="  Description with spaces  ",
            keywords="keyword1, keyword2,  keyword1,  keyword3  "
        )
        assert title == "Title with spaces"
        assert description == "Description with spaces"
        assert keywords == "keyword1, keyword2, keyword3"


if __name__ == "__main__":
    pytest.main([__file__])
