"""
Search interface component for ArchiveApp.
"""
import logging
from datetime import datetime, date
from typing import Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit,
    QComboBox, QPushButton, QDateEdit, QGroupBox, QLabel,
    QCheckBox, QFrame
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QDate
from PyQt6.QtGui import QFont

from config.settings import CATEGORIES, SEARCH_DELAY_MS

logger = logging.getLogger(__name__)


class SearchWidget(QWidget):
    """Widget for searching files with various filters."""
    
    search_requested = pyqtSignal(dict)  # Emits search parameters
    search_cleared = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)
        
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """Setup the user interface."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Title
        title_label = QLabel("Search Files")
        title_font = QFont()
        title_font.setBold(True)
        title_font.setPointSize(12)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # Separator
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(separator)
        
        # Search form
        form_layout = QFormLayout()
        
        # Title search
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("Search by title...")
        form_layout.addRow("Title:", self.title_edit)
        
        # Keywords search
        self.keywords_edit = QLineEdit()
        self.keywords_edit.setPlaceholderText("Search by keywords...")
        form_layout.addRow("Keywords:", self.keywords_edit)
        
        # Category filter
        self.category_combo = QComboBox()
        self.category_combo.addItem("All Categories", "")
        for category in CATEGORIES:
            self.category_combo.addItem(category, category)
        form_layout.addRow("Category:", self.category_combo)
        
        layout.addLayout(form_layout)
        
        # Date range group
        date_group = QGroupBox("Date Range")
        date_layout = QFormLayout(date_group)
        
        # Enable date range checkbox
        self.date_range_enabled = QCheckBox("Filter by date range")
        date_layout.addRow(self.date_range_enabled)
        
        # Start date
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addMonths(-1))
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setEnabled(False)
        date_layout.addRow("From:", self.start_date_edit)
        
        # End date
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setEnabled(False)
        date_layout.addRow("To:", self.end_date_edit)
        
        layout.addWidget(date_group)
        
        # Search buttons
        button_layout = QHBoxLayout()
        
        self.search_button = QPushButton("Search")
        self.search_button.setDefault(True)
        button_layout.addWidget(self.search_button)
        
        self.clear_button = QPushButton("Clear")
        button_layout.addWidget(self.clear_button)
        
        layout.addLayout(button_layout)
        
        # Search status
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("QLabel { color: gray; font-style: italic; }")
        layout.addWidget(self.status_label)
        
        # Add stretch to push everything to the top
        layout.addStretch()
    
    def setup_connections(self):
        """Setup signal connections."""
        # Text fields - trigger search with delay
        self.title_edit.textChanged.connect(self.on_search_text_changed)
        self.keywords_edit.textChanged.connect(self.on_search_text_changed)
        
        # Category combo - trigger immediate search
        self.category_combo.currentTextChanged.connect(self.on_search_criteria_changed)
        
        # Date range controls
        self.date_range_enabled.toggled.connect(self.on_date_range_toggled)
        self.start_date_edit.dateChanged.connect(self.on_search_criteria_changed)
        self.end_date_edit.dateChanged.connect(self.on_search_criteria_changed)
        
        # Buttons
        self.search_button.clicked.connect(self.perform_search)
        self.clear_button.clicked.connect(self.clear_search)
    
    def on_search_text_changed(self):
        """Handle text field changes with delay."""
        self.search_timer.stop()
        self.search_timer.start(SEARCH_DELAY_MS)
        self.update_status("Typing...")
    
    def on_search_criteria_changed(self):
        """Handle immediate search criteria changes."""
        self.search_timer.stop()
        self.perform_search()
    
    def on_date_range_toggled(self, enabled: bool):
        """Handle date range checkbox toggle.
        
        Args:
            enabled: Whether date range is enabled
        """
        self.start_date_edit.setEnabled(enabled)
        self.end_date_edit.setEnabled(enabled)
        self.on_search_criteria_changed()
    
    def get_search_parameters(self) -> Dict[str, Any]:
        """Get current search parameters.
        
        Returns:
            Dictionary of search parameters
        """
        params = {}
        
        # Title
        title = self.title_edit.text().strip()
        if title:
            params['title'] = title
        
        # Keywords
        keywords = self.keywords_edit.text().strip()
        if keywords:
            params['keywords'] = keywords
        
        # Category
        category = self.category_combo.currentData()
        if category:
            params['category'] = category
        
        # Date range
        if self.date_range_enabled.isChecked():
            start_date = self.start_date_edit.date().toPython()
            end_date = self.end_date_edit.date().toPython()
            
            # Convert to datetime for database query
            params['start_date'] = datetime.combine(start_date, datetime.min.time())
            params['end_date'] = datetime.combine(end_date, datetime.max.time())
        
        return params
    
    def perform_search(self):
        """Perform search with current parameters."""
        try:
            params = self.get_search_parameters()
            
            if not any(params.values()):
                # No search criteria, show all files
                self.update_status("Showing all files")
                self.search_cleared.emit()
            else:
                # Emit search parameters
                self.update_status("Searching...")
                self.search_requested.emit(params)
                
                # Update status with search criteria
                criteria_parts = []
                if 'title' in params:
                    criteria_parts.append(f"title: '{params['title']}'")
                if 'keywords' in params:
                    criteria_parts.append(f"keywords: '{params['keywords']}'")
                if 'category' in params:
                    criteria_parts.append(f"category: '{params['category']}'")
                if 'start_date' in params and 'end_date' in params:
                    start_str = params['start_date'].strftime("%Y-%m-%d")
                    end_str = params['end_date'].strftime("%Y-%m-%d")
                    criteria_parts.append(f"date: {start_str} to {end_str}")
                
                if criteria_parts:
                    status_text = "Searching by: " + ", ".join(criteria_parts)
                    self.update_status(status_text)
                
        except Exception as e:
            logger.error(f"Error performing search: {e}")
            self.update_status("Search error")
    
    def clear_search(self):
        """Clear all search fields and filters."""
        # Clear text fields
        self.title_edit.clear()
        self.keywords_edit.clear()
        
        # Reset category
        self.category_combo.setCurrentIndex(0)
        
        # Reset date range
        self.date_range_enabled.setChecked(False)
        self.start_date_edit.setDate(QDate.currentDate().addMonths(-1))
        self.end_date_edit.setDate(QDate.currentDate())
        
        # Update status and emit signal
        self.update_status("Search cleared")
        self.search_cleared.emit()
    
    def update_status(self, message: str):
        """Update status label.
        
        Args:
            message: Status message to display
        """
        self.status_label.setText(message)
    
    def set_search_results_count(self, count: int):
        """Update status with search results count.
        
        Args:
            count: Number of search results
        """
        if count == 0:
            self.update_status("No files found")
        elif count == 1:
            self.update_status("1 file found")
        else:
            self.update_status(f"{count} files found")
    
    def load_saved_search(self, params: Dict[str, Any]):
        """Load previously saved search parameters.
        
        Args:
            params: Search parameters to load
        """
        # Block signals to prevent triggering search while loading
        self.blockSignals(True)
        
        try:
            # Clear current search
            self.clear_search()
            
            # Load title
            if 'title' in params:
                self.title_edit.setText(params['title'])
            
            # Load keywords
            if 'keywords' in params:
                self.keywords_edit.setText(params['keywords'])
            
            # Load category
            if 'category' in params:
                index = self.category_combo.findData(params['category'])
                if index >= 0:
                    self.category_combo.setCurrentIndex(index)
            
            # Load date range
            if 'start_date' in params and 'end_date' in params:
                self.date_range_enabled.setChecked(True)
                
                start_date = params['start_date']
                if isinstance(start_date, datetime):
                    start_date = start_date.date()
                self.start_date_edit.setDate(QDate.fromString(start_date.isoformat(), Qt.DateFormat.ISODate))
                
                end_date = params['end_date']
                if isinstance(end_date, datetime):
                    end_date = end_date.date()
                self.end_date_edit.setDate(QDate.fromString(end_date.isoformat(), Qt.DateFormat.ISODate))
            
        finally:
            # Re-enable signals
            self.blockSignals(False)
            
            # Perform search with loaded parameters
            self.perform_search()
