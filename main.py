#!/usr/bin/env python3
"""
Main entry point for ArchiveApp - Desktop Electronic Document Management System.
"""
import sys
import os
import logging
from pathlib import Path
from PyQt6.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QPixmap, QIcon

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import (
    APP_NAME, APP_VERSION, LOG_LEVEL, LOG_FORMAT, LOG_FILE,
    ensure_directories
)
from db.migrations import initialize_database
from ui.main_window import MainWindow


def setup_logging():
    """Setup application logging."""
    try:
        # Ensure log directory exists
        LOG_FILE.parent.mkdir(parents=True, exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, LOG_LEVEL.upper()),
            format=LOG_FORMAT,
            handlers=[
                logging.FileHandler(LOG_FILE),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        logger = logging.getLogger(__name__)
        logger.info(f"Starting {APP_NAME} v{APP_VERSION}")
        logger.info(f"Log file: {LOG_FILE}")
        
    except Exception as e:
        print(f"Failed to setup logging: {e}")
        sys.exit(1)


def create_splash_screen(app: QApplication) -> QSplashScreen:
    """Create and show splash screen.
    
    Args:
        app: QApplication instance
        
    Returns:
        QSplashScreen instance
    """
    # Create a simple splash screen
    pixmap = QPixmap(400, 300)
    pixmap.fill(Qt.GlobalColor.white)
    
    splash = QSplashScreen(pixmap)
    splash.setWindowFlags(Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.FramelessWindowHint)
    splash.show()
    
    # Show loading messages
    splash.showMessage("Initializing ArchiveApp...", Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter)
    app.processEvents()
    
    return splash


def initialize_application():
    """Initialize application components."""
    logger = logging.getLogger(__name__)
    
    try:
        # Ensure all necessary directories exist
        logger.info("Creating application directories...")
        ensure_directories()
        
        # Initialize database
        logger.info("Initializing database...")
        initialize_database()
        
        logger.info("Application initialization complete")
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize application: {e}")
        return False


def show_error_dialog(title: str, message: str):
    """Show error dialog to user.
    
    Args:
        title: Dialog title
        message: Error message
    """
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Icon.Critical)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
    msg_box.exec()


def main():
    """Main application entry point."""
    # Setup logging first
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName(APP_NAME)
        app.setApplicationVersion(APP_VERSION)
        app.setOrganizationName("ArchiveApp")
        
        # Set application icon (if available)
        # app.setWindowIcon(QIcon("path/to/icon.png"))
        
        # Show splash screen
        splash = create_splash_screen(app)
        
        # Initialize application
        splash.showMessage("Setting up directories...", Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter)
        app.processEvents()
        
        if not initialize_application():
            splash.close()
            show_error_dialog("Initialization Error", 
                            "Failed to initialize the application. Please check the log file for details.")
            return 1
        
        # Create main window
        splash.showMessage("Loading main window...", Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter)
        app.processEvents()
        
        main_window = MainWindow()
        
        # Close splash screen and show main window
        splash.finish(main_window)
        main_window.show()
        
        logger.info("Application started successfully")
        
        # Start event loop
        return app.exec()
        
    except Exception as e:
        logger.error(f"Critical error in main: {e}", exc_info=True)
        show_error_dialog("Critical Error", 
                        f"A critical error occurred: {str(e)}\n\nPlease check the log file for details.")
        return 1


def handle_exception(exc_type, exc_value, exc_traceback):
    """Handle uncaught exceptions."""
    if issubclass(exc_type, KeyboardInterrupt):
        # Allow Ctrl+C to work normally
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    logger = logging.getLogger(__name__)
    logger.critical("Uncaught exception", exc_info=(exc_type, exc_value, exc_traceback))
    
    # Show error dialog
    show_error_dialog("Unexpected Error", 
                    f"An unexpected error occurred: {exc_value}\n\nPlease check the log file for details.")


if __name__ == "__main__":
    # Install global exception handler
    sys.excepthook = handle_exception
    
    # Run application
    exit_code = main()
    
    # Log shutdown
    logger = logging.getLogger(__name__)
    logger.info(f"Application shutdown with exit code: {exit_code}")
    
    sys.exit(exit_code)
