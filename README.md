# ArchiveApp - Desktop Electronic Document Management System

A comprehensive desktop application for managing and organizing electronic documents with advanced search capabilities, built with Python and PyQt6.

## Features

### Core Functionality
- **File Upload & Management**: Support for PDF, Word documents (.docx, .doc), and images (.jpg, .png, .gif, .bmp)
- **Metadata Management**: Comprehensive metadata capture including title, description, category, keywords, and upload date
- **Advanced Search**: Search by title, keywords, category, and date range with real-time filtering
- **File Organization**: Automatic file organization by date and category in local storage
- **Thumbnail Generation**: Automatic thumbnail generation for images and document preview

### User Interface
- **Modern GUI**: Clean, intuitive interface built with PyQt6
- **File List View**: Sortable table view with thumbnails, file details, and pagination
- **Context Menus**: Right-click context menus for quick file operations
- **Search Panel**: Dedicated search panel with multiple filter options
- **Progress Indicators**: Visual feedback for file operations

### Technical Features
- **SQLite Database**: Efficient local database for metadata storage
- **File Validation**: Comprehensive file type and size validation
- **Error Handling**: Robust error handling with user-friendly messages
- **Logging**: Comprehensive logging for debugging and monitoring
- **Cross-Platform**: Supports both Windows and Linux operating systems

## Installation

### Prerequisites
- Python 3.8 or higher
- pip (Python package installer)

### Step 1: Clone or Download
```bash
git clone <repository-url>
cd ArchiveApp
```

Or download and extract the ZIP file to your desired location.

### Step 2: Install Dependencies
```bash
pip install -r requirements.txt
```

### Step 3: Run the Application
```bash
python main.py
```

## Usage

### First Launch
1. Run `python main.py` to start the application
2. The application will automatically create necessary directories and initialize the database
3. The main window will open showing an empty file list

### Uploading Files
1. Click the "Upload File" button in the toolbar or use Ctrl+O
2. Select a supported file from your computer
3. Fill in the required metadata:
   - **Title**: Descriptive name for the file (required)
   - **Description**: Optional detailed description
   - **Category**: Select from predefined categories
   - **Keywords**: Comma-separated tags for easier searching
   - **Upload Date**: Automatically set to current date (editable)
4. Click "Upload" to add the file to your archive

### Searching Files
Use the search panel on the right side of the main window:

1. **Title Search**: Enter partial or complete title text
2. **Keywords Search**: Search within file keywords/tags
3. **Category Filter**: Filter by specific categories
4. **Date Range**: Enable date range filtering and select start/end dates
5. Search results update automatically as you type

### Managing Files
- **View Files**: Double-click any file to open it with the default system application
- **Edit Metadata**: Right-click a file and select "Edit Metadata" or use the toolbar button
- **Delete Files**: Right-click a file and select "Delete" or use the Delete key
- **Pagination**: Use Previous/Next buttons to navigate through large file collections

## Project Structure

```
ArchiveApp/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── README.md              # This file
├── config/
│   └── settings.py        # Application configuration
├── ui/
│   ├── main_window.py     # Main application window
│   ├── upload_dialog.py   # File upload dialog
│   └── search_widget.py   # Search interface component
├── db/
│   ├── database.py        # Database connection management
│   ├── models.py          # Data models and ORM functions
│   └── migrations.py      # Database schema setup
├── models/
│   ├── file_manager.py    # File operations
│   └── metadata.py        # Metadata handling and validation
├── utils/
│   └── helpers.py         # Utility functions
├── storage/               # File storage directory (created at runtime)
│   ├── documents/
│   ├── images/
│   └── others/
├── data/                  # Database storage (created at runtime)
└── logs/                  # Application logs (created at runtime)
```

## Configuration

The application can be configured by modifying `config/settings.py`:

- **File Types**: Add or remove supported file extensions
- **Storage Paths**: Customize storage directory locations
- **Categories**: Modify predefined file categories
- **UI Settings**: Adjust thumbnail sizes, pagination limits, etc.
- **Database**: Configure database location and settings

## Troubleshooting

### Common Issues

**Application won't start:**
- Ensure Python 3.8+ is installed
- Verify all dependencies are installed: `pip install -r requirements.txt`
- Check the log file in `logs/archive.log` for error details

**Files not uploading:**
- Verify file type is supported (PDF, DOC, DOCX, JPG, PNG, GIF, BMP)
- Check file size is under 100MB limit
- Ensure you have write permissions to the application directory

**Search not working:**
- Try clearing search filters and searching again
- Check if database file exists in `data/archive.db`
- Restart the application if search seems stuck

**Thumbnails not generating:**
- Ensure Pillow library is properly installed
- Check if image files are not corrupted
- Verify file permissions allow reading

### Log Files
Application logs are stored in `logs/archive.log` and contain detailed information about:
- Application startup and shutdown
- File operations (upload, delete, move)
- Database operations
- Error messages and stack traces

## Development

### Running Tests
```bash
pytest tests/
```

### Code Style
The project follows PEP 8 coding standards. Use tools like `flake8` or `black` for code formatting.

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## Technical Specifications

- **Language**: Python 3.8+
- **GUI Framework**: PyQt6
- **Database**: SQLite3
- **Image Processing**: Pillow (PIL)
- **File Type Detection**: python-magic
- **Testing**: pytest, pytest-qt

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, bug reports, or feature requests, please create an issue in the project repository or contact the development team.

## Version History

- **v1.0.0**: Initial release with core functionality
  - File upload and metadata management
  - Advanced search capabilities
  - Thumbnail generation
  - Cross-platform support
