"""
Main application window for ArchiveApp.
"""
import os
import logging
from typing import List, Optional
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
    QTableWidgetItem, QPushButton, QLabel, QMessageBox, QHeaderView,
    QMenu, QToolBar, QStatusBar, QSplitter, QFrame, QProgressBar
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QAction, QIcon, QPixmap

from db.models import FileRecord, file_manager as db_file_manager
from models.file_manager import file_manager
from utils.helpers import thumbnail_generator, format_helper, icon_provider
from config.settings import ITEMS_PER_PAGE

logger = logging.getLogger(__name__)


class FileTableWidget(QTableWidget):
    """Custom table widget for displaying files."""
    
    file_double_clicked = pyqtSignal(FileRecord)
    file_context_menu_requested = pyqtSignal(FileRecord, object)  # FileRecord, QPoint
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_table()
        self.files: List[FileRecord] = []
        
    def setup_table(self):
        """Setup table columns and properties."""
        columns = ["Thumbnail", "Title", "Category", "Upload Date", "Size", "Keywords"]
        self.setColumnCount(len(columns))
        self.setHorizontalHeaderLabels(columns)
        
        # Set column widths
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)  # Thumbnail
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # Title
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Category
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # Date
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # Size
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Stretch)  # Keywords
        
        self.setColumnWidth(0, 80)  # Thumbnail column
        
        # Table properties
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.setSortingEnabled(True)
        
        # Connect signals
        self.itemDoubleClicked.connect(self._on_item_double_clicked)
        self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.customContextMenuRequested.connect(self._on_context_menu_requested)
    
    def _on_item_double_clicked(self, item):
        """Handle double-click on table item."""
        row = item.row()
        if 0 <= row < len(self.files):
            self.file_double_clicked.emit(self.files[row])
    
    def _on_context_menu_requested(self, position):
        """Handle context menu request."""
        item = self.itemAt(position)
        if item:
            row = item.row()
            if 0 <= row < len(self.files):
                global_pos = self.mapToGlobal(position)
                self.file_context_menu_requested.emit(self.files[row], global_pos)
    
    def load_files(self, files: List[FileRecord]):
        """Load files into the table.
        
        Args:
            files: List of FileRecord objects to display
        """
        self.files = files
        self.setRowCount(len(files))
        
        for row, file_record in enumerate(files):
            self._populate_row(row, file_record)
    
    def _populate_row(self, row: int, file_record: FileRecord):
        """Populate a table row with file data.
        
        Args:
            row: Row index
            file_record: FileRecord to display
        """
        # Thumbnail
        thumbnail_item = QTableWidgetItem()
        thumbnail_item.setFlags(thumbnail_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
        
        # Generate thumbnail (this could be done asynchronously for better performance)
        if os.path.exists(file_record.file_path):
            pixmap = thumbnail_generator.generate_thumbnail(file_record.file_path)
            if pixmap:
                # Scale pixmap to fit cell
                scaled_pixmap = pixmap.scaled(60, 60, Qt.AspectRatioMode.KeepAspectRatio, 
                                            Qt.TransformationMode.SmoothTransformation)
                thumbnail_item.setData(Qt.ItemDataRole.DecorationRole, scaled_pixmap)
        
        self.setItem(row, 0, thumbnail_item)
        
        # Title
        title_item = QTableWidgetItem(file_record.title)
        title_item.setFlags(title_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
        self.setItem(row, 1, title_item)
        
        # Category
        category_item = QTableWidgetItem(file_record.category)
        category_item.setFlags(category_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
        self.setItem(row, 2, category_item)
        
        # Upload Date
        date_str = format_helper.format_date(file_record.upload_date)
        date_item = QTableWidgetItem(date_str)
        date_item.setFlags(date_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
        self.setItem(row, 3, date_item)
        
        # Size
        size_str = format_helper.format_file_size(file_record.file_size)
        size_item = QTableWidgetItem(size_str)
        size_item.setFlags(size_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
        self.setItem(row, 4, size_item)
        
        # Keywords
        keywords_item = QTableWidgetItem(file_record.keywords or "")
        keywords_item.setFlags(keywords_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
        self.setItem(row, 5, keywords_item)
        
        # Set row height to accommodate thumbnail
        self.setRowHeight(row, 70)
    
    def get_selected_file(self) -> Optional[FileRecord]:
        """Get the currently selected file.
        
        Returns:
            Selected FileRecord or None
        """
        current_row = self.currentRow()
        if 0 <= current_row < len(self.files):
            return self.files[current_row]
        return None


class MainWindow(QMainWindow):
    """Main application window."""
    
    def __init__(self):
        super().__init__()
        self.current_page = 0
        self.total_files = 0
        self.search_active = False
        self.search_params = {}
        
        self.setup_ui()
        self.setup_actions()
        self.setup_toolbar()
        self.setup_statusbar()
        self.setup_connections()
        
        # Load initial data
        QTimer.singleShot(100, self.refresh_file_list)
    
    def setup_ui(self):
        """Setup the user interface."""
        self.setWindowTitle("ArchiveApp - Electronic Document Management")
        self.setMinimumSize(1000, 700)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Create splitter for search panel and file list
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)

        # File table
        self.file_table = FileTableWidget()
        splitter.addWidget(self.file_table)

        # Search widget
        from .search_widget import SearchWidget
        self.search_widget = SearchWidget()
        splitter.addWidget(self.search_widget)

        # Set splitter proportions
        splitter.setSizes([600, 250])  # Give more space to file table
        
        # Pagination controls
        pagination_layout = QHBoxLayout()
        
        self.prev_button = QPushButton("Previous")
        self.prev_button.setEnabled(False)
        pagination_layout.addWidget(self.prev_button)
        
        self.page_label = QLabel("Page 1 of 1")
        pagination_layout.addWidget(self.page_label)
        
        self.next_button = QPushButton("Next")
        self.next_button.setEnabled(False)
        pagination_layout.addWidget(self.next_button)
        
        pagination_layout.addStretch()
        
        self.refresh_button = QPushButton("Refresh")
        pagination_layout.addWidget(self.refresh_button)
        
        main_layout.addLayout(pagination_layout)
    
    def setup_actions(self):
        """Setup menu actions."""
        # File menu actions
        self.upload_action = QAction("Upload File", self)
        self.upload_action.setShortcut("Ctrl+O")
        self.upload_action.triggered.connect(self.upload_file)
        
        self.exit_action = QAction("Exit", self)
        self.exit_action.setShortcut("Ctrl+Q")
        self.exit_action.triggered.connect(self.close)
        
        # Edit menu actions
        self.delete_action = QAction("Delete File", self)
        self.delete_action.setShortcut("Delete")
        self.delete_action.triggered.connect(self.delete_selected_file)
        
        self.edit_metadata_action = QAction("Edit Metadata", self)
        self.edit_metadata_action.triggered.connect(self.edit_selected_metadata)
        
        # View menu actions
        self.refresh_action = QAction("Refresh", self)
        self.refresh_action.setShortcut("F5")
        self.refresh_action.triggered.connect(self.refresh_file_list)
        
        # Create menus
        menubar = self.menuBar()
        
        file_menu = menubar.addMenu("File")
        file_menu.addAction(self.upload_action)
        file_menu.addSeparator()
        file_menu.addAction(self.exit_action)
        
        edit_menu = menubar.addMenu("Edit")
        edit_menu.addAction(self.delete_action)
        edit_menu.addAction(self.edit_metadata_action)
        
        view_menu = menubar.addMenu("View")
        view_menu.addAction(self.refresh_action)

    def setup_toolbar(self):
        """Setup toolbar."""
        toolbar = QToolBar()
        self.addToolBar(toolbar)

        toolbar.addAction(self.upload_action)
        toolbar.addSeparator()
        toolbar.addAction(self.delete_action)
        toolbar.addAction(self.edit_metadata_action)
        toolbar.addSeparator()
        toolbar.addAction(self.refresh_action)

    def setup_statusbar(self):
        """Setup status bar."""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # Progress bar for operations
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)

        self.status_bar.showMessage("Ready")

    def setup_connections(self):
        """Setup signal connections."""
        # File table connections
        self.file_table.file_double_clicked.connect(self.open_file)
        self.file_table.file_context_menu_requested.connect(self.show_context_menu)

        # Pagination connections
        self.prev_button.clicked.connect(self.previous_page)
        self.next_button.clicked.connect(self.next_page)
        self.refresh_button.clicked.connect(self.refresh_file_list)

        # Search widget connections
        self.search_widget.search_requested.connect(self.set_search_params)
        self.search_widget.search_cleared.connect(self.clear_search)

    def upload_file(self):
        """Open upload dialog."""
        from .upload_dialog import UploadDialog

        dialog = UploadDialog(self)
        if dialog.exec() == dialog.DialogCode.Accepted:
            self.refresh_file_list()
            self.status_bar.showMessage("File uploaded successfully", 3000)

    def delete_selected_file(self):
        """Delete the selected file."""
        selected_file = self.file_table.get_selected_file()
        if not selected_file:
            QMessageBox.warning(self, "No Selection", "Please select a file to delete.")
            return

        # Confirm deletion
        reply = QMessageBox.question(
            self, "Confirm Deletion",
            f"Are you sure you want to delete '{selected_file.title}'?\n"
            "This will remove both the file and its metadata.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                # Delete file from filesystem
                success, error_msg = file_manager.delete_file(selected_file.file_path)
                if not success:
                    QMessageBox.warning(self, "Delete Error", f"Failed to delete file: {error_msg}")
                    return

                # Delete from database
                db_file_manager.delete(selected_file.id)

                self.refresh_file_list()
                self.status_bar.showMessage("File deleted successfully", 3000)

            except Exception as e:
                logger.error(f"Error deleting file: {e}")
                QMessageBox.critical(self, "Error", f"Failed to delete file: {str(e)}")

    def edit_selected_metadata(self):
        """Edit metadata for the selected file."""
        selected_file = self.file_table.get_selected_file()
        if not selected_file:
            QMessageBox.warning(self, "No Selection", "Please select a file to edit.")
            return

        from .upload_dialog import UploadDialog

        dialog = UploadDialog(self, edit_mode=True, file_record=selected_file)
        if dialog.exec() == dialog.DialogCode.Accepted:
            self.refresh_file_list()
            self.status_bar.showMessage("Metadata updated successfully", 3000)

    def open_file(self, file_record: FileRecord):
        """Open file with default system application."""
        if not os.path.exists(file_record.file_path):
            QMessageBox.warning(self, "File Not Found",
                              f"The file '{file_record.title}' could not be found at:\n{file_record.file_path}")
            return

        try:
            # Open with default system application
            if os.name == 'nt':  # Windows
                os.startfile(file_record.file_path)
            elif os.name == 'posix':  # macOS and Linux
                os.system(f'open "{file_record.file_path}"' if os.uname().sysname == 'Darwin'
                         else f'xdg-open "{file_record.file_path}"')

            self.status_bar.showMessage(f"Opened '{file_record.title}'", 3000)

        except Exception as e:
            logger.error(f"Error opening file: {e}")
            QMessageBox.critical(self, "Error", f"Failed to open file: {str(e)}")

    def show_context_menu(self, file_record: FileRecord, position):
        """Show context menu for file."""
        menu = QMenu(self)

        # Open action
        open_action = menu.addAction("Open")
        open_action.triggered.connect(lambda: self.open_file(file_record))

        menu.addSeparator()

        # Edit metadata action
        edit_action = menu.addAction("Edit Metadata")
        edit_action.triggered.connect(self.edit_selected_metadata)

        # Delete action
        delete_action = menu.addAction("Delete")
        delete_action.triggered.connect(self.delete_selected_file)

        menu.exec(position)

    def refresh_file_list(self):
        """Refresh the file list."""
        try:
            self.progress_bar.setVisible(True)
            self.status_bar.showMessage("Loading files...")

            if self.search_active:
                files = db_file_manager.search(
                    limit=ITEMS_PER_PAGE,
                    offset=self.current_page * ITEMS_PER_PAGE,
                    **self.search_params
                )
                self.total_files = db_file_manager.count_search(**self.search_params)
            else:
                files = db_file_manager.get_all(
                    limit=ITEMS_PER_PAGE,
                    offset=self.current_page * ITEMS_PER_PAGE
                )
                self.total_files = db_file_manager.count_all()

            self.file_table.load_files(files)
            self.update_pagination_controls()

            # Update search widget status
            if self.search_active:
                self.search_widget.set_search_results_count(self.total_files)

            self.status_bar.showMessage(f"Loaded {len(files)} files", 3000)

        except Exception as e:
            logger.error(f"Error refreshing file list: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load files: {str(e)}")
        finally:
            self.progress_bar.setVisible(False)

    def update_pagination_controls(self):
        """Update pagination controls based on current state."""
        total_pages = max(1, (self.total_files + ITEMS_PER_PAGE - 1) // ITEMS_PER_PAGE)
        current_page_display = self.current_page + 1

        self.page_label.setText(f"Page {current_page_display} of {total_pages}")

        self.prev_button.setEnabled(self.current_page > 0)
        self.next_button.setEnabled(self.current_page < total_pages - 1)

    def previous_page(self):
        """Go to previous page."""
        if self.current_page > 0:
            self.current_page -= 1
            self.refresh_file_list()

    def next_page(self):
        """Go to next page."""
        total_pages = max(1, (self.total_files + ITEMS_PER_PAGE - 1) // ITEMS_PER_PAGE)
        if self.current_page < total_pages - 1:
            self.current_page += 1
            self.refresh_file_list()

    def set_search_params(self, **params):
        """Set search parameters and refresh list.

        Args:
            **params: Search parameters (title, keywords, category, etc.)
        """
        self.search_params = params
        self.search_active = any(params.values())
        self.current_page = 0  # Reset to first page
        self.refresh_file_list()

    def clear_search(self):
        """Clear search and show all files."""
        self.search_params = {}
        self.search_active = False
        self.current_page = 0
        self.refresh_file_list()
