"""
File operations and management for ArchiveApp.
"""
import os
import shutil
import logging
from pathlib import Path
from typing import Optional, <PERSON>ple
from datetime import datetime

from config.settings import (
    STORAGE_DIR, DOCUMENTS_DIR, IMAGES_DIR, OTHERS_DIR,
    SUPPORTED_DOCUMENT_TYPES, SUPPORTED_IMAGE_TYPES,
    MAX_FILE_SIZE
)

logger = logging.getLogger(__name__)


class FileManager:
    """Manages file operations for the archive system."""
    
    def __init__(self):
        """Initialize file manager and ensure storage directories exist."""
        self._ensure_storage_directories()
    
    def _ensure_storage_directories(self):
        """Create storage directories if they don't exist."""
        directories = [STORAGE_DIR, DOCUMENTS_DIR, IMAGES_DIR, OTHERS_DIR]
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Ensured directory exists: {directory}")
    
    def get_storage_directory(self, file_type: str) -> Path:
        """Get the appropriate storage directory for a file type.
        
        Args:
            file_type: File extension (e.g., '.pdf', '.jpg')
            
        Returns:
            Path to the appropriate storage directory
        """
        file_type = file_type.lower()
        
        if file_type in SUPPORTED_DOCUMENT_TYPES:
            return DOCUMENTS_DIR
        elif file_type in SUPPORTED_IMAGE_TYPES:
            return IMAGES_DIR
        else:
            return OTHERS_DIR
    
    def is_supported_file_type(self, file_path: str) -> bool:
        """Check if a file type is supported.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if file type is supported, False otherwise
        """
        file_extension = Path(file_path).suffix.lower()
        return file_extension in (SUPPORTED_DOCUMENT_TYPES + SUPPORTED_IMAGE_TYPES)
    
    def validate_file(self, file_path: str) -> Tuple[bool, str]:
        """Validate a file for archiving.
        
        Args:
            file_path: Path to the file to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        file_path = Path(file_path)
        
        # Check if file exists
        if not file_path.exists():
            return False, "File does not exist"
        
        # Check if it's a file (not directory)
        if not file_path.is_file():
            return False, "Path is not a file"
        
        # Check file size
        file_size = file_path.stat().st_size
        if file_size > MAX_FILE_SIZE:
            return False, f"File size ({file_size} bytes) exceeds maximum allowed size ({MAX_FILE_SIZE} bytes)"
        
        # Check file type
        if not self.is_supported_file_type(str(file_path)):
            return False, f"File type '{file_path.suffix}' is not supported"
        
        return True, ""
    
    def generate_unique_filename(self, original_filename: str, storage_dir: Path) -> str:
        """Generate a unique filename to avoid conflicts.
        
        Args:
            original_filename: Original name of the file
            storage_dir: Directory where the file will be stored
            
        Returns:
            Unique filename
        """
        base_name = Path(original_filename).stem
        extension = Path(original_filename).suffix
        
        # Start with original filename
        counter = 0
        while True:
            if counter == 0:
                filename = original_filename
            else:
                filename = f"{base_name}_{counter}{extension}"
            
            if not (storage_dir / filename).exists():
                return filename
            
            counter += 1
    
    def copy_file_to_archive(self, source_path: str, title: str) -> Tuple[bool, str, str]:
        """Copy a file to the archive storage.
        
        Args:
            source_path: Path to the source file
            title: Title for the file (used for organizing)
            
        Returns:
            Tuple of (success, destination_path, error_message)
        """
        try:
            source_path = Path(source_path)
            
            # Validate file
            is_valid, error_msg = self.validate_file(str(source_path))
            if not is_valid:
                return False, "", error_msg
            
            # Get storage directory
            file_extension = source_path.suffix.lower()
            storage_dir = self.get_storage_directory(file_extension)
            
            # Create subdirectory based on current date
            date_subdir = storage_dir / datetime.now().strftime("%Y/%m")
            date_subdir.mkdir(parents=True, exist_ok=True)
            
            # Generate unique filename
            unique_filename = self.generate_unique_filename(source_path.name, date_subdir)
            destination_path = date_subdir / unique_filename
            
            # Copy file
            shutil.copy2(source_path, destination_path)
            
            logger.info(f"File copied from {source_path} to {destination_path}")
            return True, str(destination_path), ""
            
        except Exception as e:
            error_msg = f"Failed to copy file: {str(e)}"
            logger.error(error_msg)
            return False, "", error_msg
    
    def delete_file(self, file_path: str) -> Tuple[bool, str]:
        """Delete a file from the archive storage.
        
        Args:
            file_path: Path to the file to delete
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                return False, "File does not exist"
            
            file_path.unlink()
            logger.info(f"File deleted: {file_path}")
            
            # Try to remove empty parent directories
            self._cleanup_empty_directories(file_path.parent)
            
            return True, ""
            
        except Exception as e:
            error_msg = f"Failed to delete file: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def _cleanup_empty_directories(self, directory: Path):
        """Remove empty directories up the tree.
        
        Args:
            directory: Directory to start cleanup from
        """
        try:
            # Only clean up directories within our storage structure
            if not str(directory).startswith(str(STORAGE_DIR)):
                return
            
            # Don't remove the main storage directories
            if directory in [STORAGE_DIR, DOCUMENTS_DIR, IMAGES_DIR, OTHERS_DIR]:
                return
            
            # Remove if empty
            if directory.exists() and not any(directory.iterdir()):
                directory.rmdir()
                logger.debug(f"Removed empty directory: {directory}")
                
                # Recursively check parent
                self._cleanup_empty_directories(directory.parent)
                
        except Exception as e:
            logger.debug(f"Could not remove directory {directory}: {e}")
    
    def move_file(self, old_path: str, new_path: str) -> Tuple[bool, str]:
        """Move a file within the archive storage.
        
        Args:
            old_path: Current path of the file
            new_path: New path for the file
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            old_path = Path(old_path)
            new_path = Path(new_path)
            
            if not old_path.exists():
                return False, "Source file does not exist"
            
            # Ensure destination directory exists
            new_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Move file
            shutil.move(str(old_path), str(new_path))
            
            logger.info(f"File moved from {old_path} to {new_path}")
            
            # Cleanup empty directories
            self._cleanup_empty_directories(old_path.parent)
            
            return True, ""
            
        except Exception as e:
            error_msg = f"Failed to move file: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def get_file_info(self, file_path: str) -> Optional[dict]:
        """Get information about a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Dictionary with file information or None if file doesn't exist
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                return None
            
            stat = file_path.stat()
            
            return {
                'name': file_path.name,
                'size': stat.st_size,
                'extension': file_path.suffix.lower(),
                'modified_time': datetime.fromtimestamp(stat.st_mtime),
                'created_time': datetime.fromtimestamp(stat.st_ctime),
                'exists': True
            }
            
        except Exception as e:
            logger.error(f"Failed to get file info for {file_path}: {e}")
            return None


# Global file manager instance
file_manager = FileManager()
