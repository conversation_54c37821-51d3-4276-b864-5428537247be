"""
Metadata handling and validation for ArchiveApp.
"""
import re
import logging
from typing import List, Tu<PERSON>, Optional
from datetime import datetime

from config.settings import MAX_TITLE_LENGTH, CATEGORIES

logger = logging.getLogger(__name__)


class MetadataValidator:
    """Validates metadata for file records."""
    
    @staticmethod
    def validate_title(title: str) -> Tuple[bool, str]:
        """Validate file title.
        
        Args:
            title: Title to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not title or not title.strip():
            return False, "Title is required"
        
        title = title.strip()
        
        if len(title) > MAX_TITLE_LENGTH:
            return False, f"Title must be {MAX_TITLE_LENGTH} characters or less"
        
        # Check for invalid characters (basic validation)
        invalid_chars = ['<', '>', ':', '"', '|', '?', '*']
        for char in invalid_chars:
            if char in title:
                return False, f"Title contains invalid character: {char}"
        
        return True, ""
    
    @staticmethod
    def validate_description(description: str) -> <PERSON><PERSON>[bool, str]:
        """Validate file description.
        
        Args:
            description: Description to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Description is optional, so empty is valid
        if not description:
            return True, ""
        
        # Basic length check (reasonable limit)
        if len(description) > 10000:
            return False, "Description must be 10,000 characters or less"
        
        return True, ""
    
    @staticmethod
    def validate_category(category: str) -> Tuple[bool, str]:
        """Validate file category.
        
        Args:
            category: Category to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not category:
            return False, "Category is required"
        
        if category not in CATEGORIES:
            return False, f"Category must be one of: {', '.join(CATEGORIES)}"
        
        return True, ""
    
    @staticmethod
    def validate_keywords(keywords: str) -> Tuple[bool, str]:
        """Validate keywords string.
        
        Args:
            keywords: Comma-separated keywords to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Keywords are optional
        if not keywords:
            return True, ""
        
        # Basic length check
        if len(keywords) > 1000:
            return False, "Keywords must be 1,000 characters or less"
        
        return True, ""
    
    @staticmethod
    def validate_upload_date(upload_date: datetime) -> Tuple[bool, str]:
        """Validate upload date.
        
        Args:
            upload_date: Upload date to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not upload_date:
            return False, "Upload date is required"
        
        # Check if date is not in the future
        if upload_date > datetime.now():
            return False, "Upload date cannot be in the future"
        
        # Check if date is not too far in the past (reasonable limit)
        min_date = datetime(1900, 1, 1)
        if upload_date < min_date:
            return False, "Upload date is too far in the past"
        
        return True, ""


class MetadataProcessor:
    """Processes and normalizes metadata."""
    
    @staticmethod
    def normalize_title(title: str) -> str:
        """Normalize title by trimming whitespace and removing extra spaces.
        
        Args:
            title: Title to normalize
            
        Returns:
            Normalized title
        """
        if not title:
            return ""
        
        # Trim whitespace and collapse multiple spaces
        normalized = re.sub(r'\s+', ' ', title.strip())
        return normalized
    
    @staticmethod
    def normalize_description(description: str) -> str:
        """Normalize description by trimming whitespace.
        
        Args:
            description: Description to normalize
            
        Returns:
            Normalized description
        """
        if not description:
            return ""
        
        return description.strip()
    
    @staticmethod
    def normalize_keywords(keywords: str) -> str:
        """Normalize keywords by cleaning and formatting.
        
        Args:
            keywords: Comma-separated keywords to normalize
            
        Returns:
            Normalized keywords string
        """
        if not keywords:
            return ""
        
        # Split by comma, trim each keyword, remove empty ones
        keyword_list = [kw.strip() for kw in keywords.split(',') if kw.strip()]
        
        # Remove duplicates while preserving order
        seen = set()
        unique_keywords = []
        for kw in keyword_list:
            kw_lower = kw.lower()
            if kw_lower not in seen:
                seen.add(kw_lower)
                unique_keywords.append(kw)
        
        return ', '.join(unique_keywords)
    
    @staticmethod
    def extract_keywords_from_title(title: str) -> List[str]:
        """Extract potential keywords from title.
        
        Args:
            title: Title to extract keywords from
            
        Returns:
            List of potential keywords
        """
        if not title:
            return []
        
        # Simple keyword extraction - split by common separators
        # and filter out common words
        common_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to',
            'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be',
            'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
            'will', 'would', 'could', 'should', 'may', 'might', 'must'
        }
        
        # Split by various separators
        words = re.split(r'[\s\-_.,;:!?()[\]{}]+', title.lower())
        
        # Filter out empty strings, common words, and very short words
        keywords = [
            word for word in words
            if len(word) > 2 and word not in common_words
        ]
        
        return keywords
    
    @staticmethod
    def suggest_category_from_extension(file_extension: str) -> str:
        """Suggest category based on file extension.
        
        Args:
            file_extension: File extension (e.g., '.pdf')
            
        Returns:
            Suggested category
        """
        extension = file_extension.lower()
        
        if extension in ['.pdf', '.doc', '.docx']:
            return "Documents"
        elif extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
            return "Images"
        else:
            return "Other"


class MetadataManager:
    """Manages metadata operations."""
    
    def __init__(self):
        self.validator = MetadataValidator()
        self.processor = MetadataProcessor()
    
    def validate_all(self, title: str, description: str, category: str,
                    keywords: str, upload_date: datetime) -> Tuple[bool, List[str]]:
        """Validate all metadata fields.
        
        Args:
            title: File title
            description: File description
            category: File category
            keywords: File keywords
            upload_date: Upload date
            
        Returns:
            Tuple of (is_valid, list_of_error_messages)
        """
        errors = []
        
        # Validate each field
        is_valid, error = self.validator.validate_title(title)
        if not is_valid:
            errors.append(f"Title: {error}")
        
        is_valid, error = self.validator.validate_description(description)
        if not is_valid:
            errors.append(f"Description: {error}")
        
        is_valid, error = self.validator.validate_category(category)
        if not is_valid:
            errors.append(f"Category: {error}")
        
        is_valid, error = self.validator.validate_keywords(keywords)
        if not is_valid:
            errors.append(f"Keywords: {error}")
        
        is_valid, error = self.validator.validate_upload_date(upload_date)
        if not is_valid:
            errors.append(f"Upload date: {error}")
        
        return len(errors) == 0, errors
    
    def normalize_all(self, title: str, description: str, keywords: str) -> Tuple[str, str, str]:
        """Normalize all metadata fields.
        
        Args:
            title: File title
            description: File description
            keywords: File keywords
            
        Returns:
            Tuple of (normalized_title, normalized_description, normalized_keywords)
        """
        normalized_title = self.processor.normalize_title(title)
        normalized_description = self.processor.normalize_description(description)
        normalized_keywords = self.processor.normalize_keywords(keywords)
        
        return normalized_title, normalized_description, normalized_keywords


# Global metadata manager instance
metadata_manager = MetadataManager()
